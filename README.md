# 游戏昵称生成器

这个Python程序使用Faker库生成各种风格的游戏英文昵称。

## 安装依赖

在运行程序前，请先安装Faker库：

```bash
pip install faker
```

## 使用方法

1. 运行程序：
```bash
python game_nickname_generator.py
```

2. 程序会提示输入要生成的昵称数量（默认100个）

3. 生成的昵称会保存到 `game_nicknames.json` 文件中

## 生成的昵称类型

程序会生成8种不同风格的昵称：

1. **前缀 + 用户名**: 如 <PERSON>Johnson, ShadowSmith
2. **用户名 + 后缀**: 如 MillerHunter, BrownSlayer  
3. **前缀 + 后缀**: 如 FireWarrior, IceMage
4. **前缀 + 名字 + 后缀**: 如 <PERSON><PERSON><PERSON>nHunter, BloodMarySlayer
5. **简单用户名**: 如 anderson, miller
6. **颜色 + 动物**: 如 RedWolf, BlueDragon
7. **两个单词组合**: 如 StormBlade, FireStorm
8. **公司名称风格**: 如 TechCorp, GameStudio

## 输出格式

生成的JSON文件包含：
- 生成时间
- 昵称总数
- 昵称列表

```json
{
  "generated_at": "2025-06-27 15:30:45",
  "total_count": 100,
  "nicknames": [
    "DarkHunter",
    "ShadowWolf777",
    "FireMage",
    ...
  ]
}
```
