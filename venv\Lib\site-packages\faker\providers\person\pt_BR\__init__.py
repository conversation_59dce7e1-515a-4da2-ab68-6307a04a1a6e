from .. import Provider as Person<PERSON>rovider


class Provider(PersonProvider):
    formats_female = (
        "{{first_name_female}} {{last_name}}",
        "{{first_name_female}} {{last_name}}",
        "{{first_name_female}} {{last_name}}",
        "{{first_name_female}} {{last_name}}",
        "{{first_name_female}} {{last_name}}",
        "{{prefix_female}} {{first_name_female}} {{last_name}}",
    )

    formats_male = (
        "{{first_name_male}} {{last_name}}",
        "{{first_name_male}} {{last_name}}",
        "{{first_name_male}} {{last_name}}",
        "{{first_name_male}} {{last_name}}",
        "{{first_name_male}} {{last_name}}",
        "{{prefix_male}} {{first_name_male}} {{last_name}}",
    )

    formats = formats_male + formats_female

    """
    To a previous (undocumented?) list of female given names was added the 100
    most popular names in Brazil in 2014 and 2015 according to Exame magazine:
    * http://exame.abril.com.br/brasil/noticias/os-100-nomes-mais-comuns-no-brasil-em-2014
    * http://exame.abril.com.br/brasil/noticias/os-100-nomes-mais-comuns-no-brasil-em-2015
    Merged with this list from 2023
    * https://brasil.babycenter.com/a25034220/200-nomes-mais-populares-para-beb%C3%AAs-no-1%C2%BA-semestre-de-2023
    """
    first_names_female = (
        "Agatha",
        "Alana",
        "Alexia",
        "Alice",
        "Allana",
        "Alícia",
        "Amanda",
        "Ana",
        "Ana Beatriz",
        "Ana Carolina",
        "Ana Cecília",
        "Ana Clara",
        "Ana Julia",
        "Ana Júlia",
        "Ana Laura",
        "Ana Liz",
        "Ana Luiza",
        "Ana Lívia",
        "Ana Sophia",
        "Ana Vitória",
        "Anna Liz",
        "Antonella",
        "Aurora",
        "Ayla",
        "Aylla",
        "Beatriz",
        "Bella",
        "Bianca",
        "Brenda",
        "Bruna",
        "Bárbara",
        "Camila",
        "Carolina",
        "Caroline",
        "Catarina",
        "Cecilia",
        "Cecília",
        "Clara",
        "Clarice",
        "Daniela",
        "Eduarda",
        "Elisa",
        "Eloah",
        "Eloá",
        "Emanuella",
        "Emanuelly",
        "Emilly",
        "Ester",
        "Esther",
        "Evelyn",
        "Fernanda",
        "Gabriela",
        "Gabrielly",
        "Giovanna",
        "Hadassa",
        "Helena",
        "Hellena",
        "Heloisa",
        "Heloísa",
        "Isabel",
        "Isabela",
        "Isabella",
        "Isabelly",
        "Isadora",
        "Isis",
        "Jade",
        "Joana",
        "Julia",
        "Juliana",
        "Júlia",
        "Kamilly",
        "Lara",
        "Larissa",
        "Laura",
        "Lavínia",
        "Laís",
        "Letícia",
        "Liz",
        "Lorena",
        "Luana",
        "Luara",
        "Luiza",
        "Luna",
        "Lunna",
        "Luísa",
        "Lívia",
        "Maitê",
        "Manuela",
        "Manuella",
        "Marcela",
        "Maria",
        "Maria Alice",
        "Maria Cecília",
        "Maria Clara",
        "Maria Eduarda",
        "Maria Fernanda",
        "Maria Flor",
        "Maria Helena",
        "Maria Isis",
        "Maria Julia",
        "Maria Júlia",
        "Maria Laura",
        "Maria Liz",
        "Maria Luiza",
        "Maria Luísa",
        "Maria Sophia",
        "Maria Vitória",
        "Mariah",
        "Mariana",
        "Mariane",
        "Marina",
        "Maya",
        "Maysa",
        "Melina",
        "Melissa",
        "Milena",
        "Mirella",
        "Natália",
        "Nicole",
        "Nina",
        "Olivia",
        "Olívia",
        "Pietra",
        "Rafaela",
        "Raquel",
        "Rebeca",
        "Sabrina",
        "Sara",
        "Sarah",
        "Sofia",
        "Sophia",
        "Sophie",
        "Stella",
        "Stephany",
        "Valentina",
        "Vitória",
        "Yasmin",
        "Zoe",
        "Ágatha",
        "Ísis",
    )

    """
    To a previous (undocumented?) list of male given names was added the 100
    most popular names in Brazil in 2014 and 2015 according to this blog post:
    * http://exame.abril.com.br/brasil/noticias/os-100-nomes-mais-comuns-no-brasil-em-2014
    * http://exame.abril.com.br/brasil/noticias/os-100-nomes-mais-comuns-no-brasil-em-2015
    Merged with this list from 2023
    * https://brasil.babycenter.com/a25034220/200-nomes-mais-populares-para-beb%C3%AAs-no-1%C2%BA-semestre-de-2023
    """
    first_names_male = (
        "Alexandre",
        "André",
        "Anthony",
        "Anthony Gabriel",
        "Antony",
        "Antônio",
        "Apollo",
        "Arthur",
        "Arthur Gabriel",
        "Arthur Miguel",
        "Asafe",
        "Augusto",
        "Benicio",
        "Benjamim",
        "Benjamin",
        "Bento",
        "Benício",
        "Bernardo",
        "Brayan",
        "Breno",
        "Bruno",
        "Bryan",
        "Caio",
        "Caleb",
        "Calebe",
        "Carlos Eduardo",
        "Cauã",
        "Cauê",
        "Daniel",
        "Danilo",
        "Dante",
        "Davi",
        "Davi Lucas",
        "Davi Lucca",
        "Davi Luiz",
        "Davi Miguel",
        "Diego",
        "Diogo",
        "Dom",
        "Eduardo",
        "Emanuel",
        "Enrico",
        "Enzo",
        "Enzo Gabriel",
        "Erick",
        "Felipe",
        "Fernando",
        "Francisco",
        "Gabriel",
        "Gael",
        "Gael Henrique",
        "Guilherme",
        "Gustavo",
        "Gustavo Henrique",
        "Heitor",
        "Henrique",
        "Henry",
        "Henry Gabriel",
        "Ian",
        "Igor",
        "Isaac",
        "Isaque",
        "Joaquim",
        "Josué",
        "José",
        "José Miguel",
        "José Pedro",
        "João",
        "João Felipe",
        "João Gabriel",
        "João Guilherme",
        "João Lucas",
        "João Miguel",
        "João Pedro",
        "João Vitor",
        "Juan",
        "Kaique",
        "Kevin",
        "Leandro",
        "Leonardo",
        "Levi",
        "Liam",
        "Lorenzo",
        "Luan",
        "Lucas",
        "Lucas Gabriel",
        "Lucca",
        "Luigi",
        "Luiz Felipe",
        "Luiz Fernando",
        "Luiz Gustavo",
        "Luiz Henrique",
        "Luiz Miguel",
        "Luiz Otávio",
        "Léo",
        "Marcelo",
        "Marcos Vinicius",
        "Mateus",
        "Matheus",
        "Mathias",
        "Matteo",
        "Miguel",
        "Murilo",
        "Nathan",
        "Nicolas",
        "Noah",
        "Oliver",
        "Otto",
        "Otávio",
        "Paulo",
        "Pedro",
        "Pedro Henrique",
        "Pedro Lucas",
        "Pedro Miguel",
        "Pietro",
        "Rael",
        "Rafael",
        "Raul",
        "Ravi",
        "Ravi Lucca",
        "Ravy",
        "Renan",
        "Rhavi",
        "Rodrigo",
        "Ryan",
        "Samuel",
        "Thales",
        "Theo",
        "Theodoro",
        "Thiago",
        "Thomas",
        "Théo",
        "Valentim",
        "Vicente",
        "Vinicius",
        "Vinícius",
        "Vitor",
        "Vitor Gabriel",
        "Vitor Hugo",
        "Yago",
        "Yan",
        "Yuri",
    )

    first_names = first_names_male + first_names_female

    """
    To a previous (undocumented?) list of family names was added the 70
    most popular family names in Brazil according to this blog post:
    * http://nomeschiques.com/os-70-sobrenomes-mais-comuns-e-famosos-do-brasil/
    Merged with this list from 2023
    * https://nomescriativos.com.br/sobrenomes-mais-comuns/
    """
    last_names = (
        "Abreu",
        "Albuquerque",
        "Almeida",
        "Alves",
        "Andrade",
        "Aparecida",
        "Aragão",
        "Araújo",
        "Azevedo",
        "Barbosa",
        "Barros",
        "Borges",
        "Brito",
        "Caldeira",
        "Camargo",
        "Campos",
        "Cardoso",
        "Carvalho",
        "Casa Grande",
        "Cassiano",
        "Castro",
        "Cavalcante",
        "Cavalcanti",
        "Cirino",
        "Correia",
        "Costa",
        "Costela",
        "Cunha",
        "Câmara",
        "da Conceição",
        "da Costa",
        "da Cruz",
        "da Cunha",
        "da Luz",
        "da Mata",
        "da Mota",
        "da Paz",
        "da Rocha",
        "da Rosa",
        "das Neves",
        "Dias",
        "Duarte",
        "Farias",
        "Fernandes",
        "Ferreira",
        "Fogaça",
        "Fonseca",
        "Freitas",
        "Garcia",
        "Gomes",
        "Gonçalves",
        "Guerra",
        "Jesus",
        "Leão",
        "Lima",
        "Lopes",
        "Macedo",
        "Machado",
        "Marques",
        "Martins",
        "Melo",
        "Mendes",
        "Mendonça",
        "Monteiro",
        "Montenegro",
        "Moraes",
        "Moreira",
        "Moura",
        "Nascimento",
        "Nogueira",
        "Novaes",
        "Novais",
        "Nunes",
        "Oliveira",
        "Pacheco",
        "Pastor",
        "Peixoto",
        "Pereira",
        "Pimenta",
        "Pinto",
        "Pires",
        "Porto",
        "Ramos",
        "Rezende",
        "Ribeiro",
        "Rios",
        "Rocha",
        "Rodrigues",
        "Sales",
        "Sampaio",
        "Santos",
        "Silva",
        "Silveira",
        "Siqueira",
        "Sousa",
        "Souza",
        "Sá",
        "Teixeira",
        "Vargas",
        "Vasconcelos",
        "Viana",
        "Vieira",
    )

    prefixes_female = ("Srta.", "Sra.", "Dra.")
    prefixes_male = ("Sr.", "Dr.")
