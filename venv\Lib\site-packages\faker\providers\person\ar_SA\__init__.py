from ..ar_AA import Provider as ArabicPersonProvider


class Provider(ArabicPersonProvider):
    last_names = (
        "آل الشيخ",
        "آل العسكري",
        "آل بن ظافر",
        "آل بن لافي",
        "آل جعفر",
        "آل حسين",
        "آل خضير",
        "آل رفيع",
        "آل سعود",
        "آل سلطان",
        "آل صفوان",
        "آل عايض",
        "آل عطفة",
        "آل علي",
        "آل عواض",
        "آل قصير",
        "آل محمد بن علي بن جماز",
        "آل معيض",
        "آل مقطة",
        "أبا الخيل",
        "أبو داوود",
        "الجابر",
        "الجفالي",
        "الحجار",
        "الحكير",
        "الخرافي",
        "الدباغ",
        "الراشد",
        "الشايع",
        "العجلان",
        "العقيل",
        "العليان",
        "المشاولة",
        "المغاولة",
        "المهنا",
        "المهيدب",
        "بقشان",
        "بن لادن",
        "حجار",
        "حنبولي",
        "شربتلي",
        "فصيل",
        "كانو",
        "مهنا",
    )
