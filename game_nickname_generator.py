#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏英文昵称生成器
使用Faker库生成各种风格的游戏昵称并保存为JSON格式
"""

from faker import Faker
import json
import random
import os
from datetime import datetime

def generate_game_nicknames(count=100):
    """
    生成游戏昵称
    
    Args:
        count (int): 生成昵称的数量
    
    Returns:
        list: 包含生成昵称的列表
    """
    fake = Faker('en_US')
    nicknames = []
    
    # 游戏风格的前缀和后缀
    prefixes = [
        '<PERSON>', 'Shadow', 'Fire', 'Ice', 'Storm', 'Blood', 'Death', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'My<PERSON>', '<PERSON>', 'Elite', 'Epic',
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
    ]
    
    suffixes = [
        '<PERSON>', 'Slayer', '<PERSON>', '<PERSON>', 'Ma<PERSON>', 'Assassin', '<PERSON>',
        'Master', 'Lord', 'King', 'Queen', '<PERSON>', '<PERSON>', 'Hero', 'Champion',
        '<PERSON>', '<PERSON>rusher', '<PERSON>', '<PERSON>', 'Fire', 'Shadow', 'Wolf',
        'Dragon', 'Phoenix', 'Viper', 'Tiger', 'Eagle', 'Reaper', 'Striker'
    ]
    
    # 数字后缀选项
    number_suffixes = ['', '7', '13', '21', '69', '88', '99', '777', '2024', '2025']
    
    for i in range(count):
        nickname_type = random.randint(1, 8)
        
        if nickname_type == 1:
            # 类型1: 前缀 + 用户名
            nickname = f"{random.choice(prefixes)}{fake.user_name()}"
        elif nickname_type == 2:
            # 类型2: 用户名 + 后缀
            nickname = f"{fake.user_name()}{random.choice(suffixes)}"
        elif nickname_type == 3:
            # 类型3: 前缀 + 后缀
            nickname = f"{random.choice(prefixes)}{random.choice(suffixes)}"
        elif nickname_type == 4:
            # 类型4: 前缀 + 名字 + 后缀
            first_name = fake.first_name()
            nickname = f"{random.choice(prefixes)}{first_name}{random.choice(suffixes)}"
        elif nickname_type == 5:
            # 类型5: 单纯的用户名
            nickname = fake.user_name()
        elif nickname_type == 6:
            # 类型6: 颜色 + 动物
            color = fake.color_name()
            # 简单的动物列表
            animals = ['Wolf', 'Tiger', 'Eagle', 'Dragon', 'Lion', 'Bear', 'Fox', 'Hawk']
            nickname = f"{color}{random.choice(animals)}"
        elif nickname_type == 7:
            # 类型7: 两个单词组合
            word1 = fake.word().capitalize()
            word2 = fake.word().capitalize()
            nickname = f"{word1}{word2}"
        else:
            # 类型8: 公司名称风格
            nickname = fake.company().replace(' ', '').replace(',', '').replace('.', '')
        
        # 添加数字后缀 (30% 概率)
        if random.random() < 0.3:
            nickname += random.choice(number_suffixes)
        
        # 确保唯一性
        if nickname not in nicknames:
            nicknames.append(nickname)
    
    return nicknames

def save_to_json(nicknames, filename='game_nicknames.json'):
    """
    将昵称保存到JSON文件
    
    Args:
        nicknames (list): 昵称列表
        filename (str): 输出文件名
    """
    output_data = {
        'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'total_count': len(nicknames),
        'nicknames': nicknames
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    print(f"成功生成 {len(nicknames)} 个游戏昵称，已保存到 {filename}")

def main():
    """主函数"""
    print("游戏英文昵称生成器")
    print("=" * 50)
    
    # 获取用户输入
    try:
        count = int(input("请输入要生成的昵称数量 (默认100): ") or "100")
    except ValueError:
        count = 100
        print("输入无效，使用默认值100")
    
    # 生成昵称
    print(f"\n正在生成 {count} 个游戏昵称...")
    nicknames = generate_game_nicknames(count)
    
    # 保存到JSON文件
    output_file = 'game_nicknames.json'
    save_to_json(nicknames, output_file)
    
    # 显示部分结果
    print(f"\n生成的昵称示例 (前10个):")
    for i, nickname in enumerate(nicknames[:10], 1):
        print(f"{i:2d}. {nickname}")
    
    if len(nicknames) > 10:
        print(f"... 还有 {len(nicknames) - 10} 个昵称")
    
    print(f"\n完整列表已保存到: {os.path.abspath(output_file)}")

if __name__ == "__main__":
    main()
