from collections import OrderedDict

from .. import Provider as PersonProvider


class Provider(PersonProvider):

    # Source for last names: https://nachnamen.net/luxemburg
    last_names = OrderedDict(
        (
            ("<PERSON><PERSON><PERSON>", 6799),
            ("<PERSON>", 5784),
            ("<PERSON>", 4858),
            ("<PERSON>", 4837),
            ("<PERSON>", 4628),
            ("<PERSON><PERSON>", 3304),
            ("<PERSON><PERSON><PERSON>", 3135),
            ("Schroeder", 2839),
            ("<PERSON>", 2549),
            ("<PERSON>", 2413),
            ("<PERSON>", 2159),
            ("Da silva", 2007),
            ("<PERSON><PERSON><PERSON>", 1949),
            ("<PERSON><PERSON>", 1944),
            ("<PERSON><PERSON><PERSON>", 1891),
            ("<PERSON>s santos", 1867),
            ("We<PERSON>", 1788),
            ("<PERSON>", 1785),
            ("<PERSON>", 1721),
            ("<PERSON>", 1657),
            ("<PERSON>", 1614),
            ("<PERSON><PERSON><PERSON>", 1605),
            ("<PERSON>", 1580),
            ("<PERSON><PERSON>", 1446),
            ("<PERSON>", 1381),
            ("<PERSON><PERSON><PERSON>", 1368),
            ("<PERSON><PERSON>", 1352),
            ("<PERSON><PERSON>", 1351),
            ("<PERSON><PERSON><PERSON>", 1350),
            ("<PERSON><PERSON>", 1311),
            ("<PERSON><PERSON><PERSON>", 1301),
            ("<PERSON><PERSON>", 1239),
            ("<PERSON><PERSON>", 1203),
            ("<PERSON><PERSON><PERSON>", 1153),
            ("<PERSON><PERSON><PERSON><PERSON>", 1151),
            ("<PERSON><PERSON>", 1148),
            ("<PERSON><PERSON>", 1135),
            ("<PERSON>", 1119),
            ("<PERSON><PERSON>h", 1090),
            ("<PERSON>rig<PERSON>", 1074),
            ("<PERSON>", 1065),
            ("<PERSON>", 1062),
            ("Go<PERSON>", 1043),
            ("<PERSON><PERSON>or", 1030),
            ("<PERSON><PERSON>", 1021),
            ("Wolff", 961),
            ("Martins", 952),
            ("Heinen", 914),
            ("Weydert", 891),
            ("Zimmer", 889),
            ("Goergen", 867),
            ("Fischer", 863),
            ("Wagener", 854),
            ("Reding", 837),
            ("Lentz", 830),
            ("Flammang", 828),
            ("Bernard", 827),
            ("Scholtes", 809),
            ("Adrovic", 800),
            ("Koch", 775),
            ("Goedert", 763),
            ("Arend", 753),
            ("Winandy", 753),
            ("Jacoby", 740),
            ("Nilles", 703),
            ("Gengler", 690),
            ("Peters", 690),
            ("Berg", 685),
            ("Lanners", 684),
            ("Pinto", 676),
            ("Sabotic", 673),
            ("Back", 672),
            ("Lopes", 663),
            ("Marques", 658),
            ("Lux", 655),
            ("Bertemes", 652),
            ("Putz", 649),
            ("Jung", 648),
            ("Haas", 633),
            ("Erpelding", 630),
            ("Schmitt", 620),
            ("Weiler", 613),
            ("Mangen", 607),
            ("Pauly", 602),
            ("Weyland", 601),
            ("Dostert", 599),
            ("Biver", 598),
            ("Alves", 597),
            ("Huberty", 594),
            ("Schreiner", 590),
            ("Decker", 590),
            ("Backes", 589),
            ("Schaus", 589),
            ("Olinger", 576),
            ("Rastoder", 562),
            ("Schaack", 561),
            ("Grethen", 554),
            ("Steichen", 542),
            ("Mendes", 541),
            ("Monteiro", 539),
            ("Oliveira", 536),
            ("Lucas", 536),
            ("Poos", 536),
            ("Ney", 535),
            ("Teixeira", 528),
            ("Michels", 527),
            ("Wirtz", 515),
            ("Mathieu", 511),
            ("Schintgen", 510),
            ("Scheer", 493),
            ("Peiffer", 486),
            ("Hilbert", 485),
            ("Thein", 478),
            ("Steinmetz", 470),
            ("Stoffel", 470),
            ("Da costa", 469),
            ("Arendt", 468),
            ("Clement", 468),
            ("Hermes", 465),
            ("Dumont", 463),
            ("Kohn", 459),
            ("Wies", 459),
            ("Feller", 454),
            ("Soares", 454),
            ("Kneip", 453),
            ("Kohl", 448),
            ("De sousa", 441),
            ("Thinnes", 439),
            ("Almeida", 437),
            ("Elsen", 436),
            ("Glod", 433),
            ("Mergen", 432),
            ("Trausch", 432),
            ("Mertens", 430),
            ("Schaeffer", 425),
            ("Mousel", 424),
            ("Heck", 419),
            ("Thiel", 418),
            ("Duarte", 418),
            ("Lang", 416),
            ("Mersch", 414),
            ("Linden", 414),
            ("Thiry", 413),
            ("Muhovic", 411),
            ("Bausch", 411),
            ("Georges", 410),
            ("Lambert", 403),
            ("Hengen", 403),
            ("Konsbruck", 397),
            ("Trierweiler", 395),
            ("Ewen", 393),
            ("Kohnen", 391),
            ("Berchem", 388),
            ("Schmidt", 387),
            ("Thoma", 384),
            ("Heiderscheid", 383),
            ("May", 382),
            ("Wantz", 381),
            ("Clemens", 380),
            ("Conter", 379),
            ("Felten", 377),
            ("Gerard", 377),
            ("Garcia", 376),
            ("Ribeiro", 372),
            ("Skrijelj", 370),
            ("Wolter", 369),
            ("Lorang", 361),
            ("Nickels", 360),
            ("Barthel", 359),
            ("Huss", 358),
            ("Jeitz", 358),
            ("Moes", 357),
            ("Werner", 357),
            ("Kerschen", 354),
            ("Sinner", 352),
            ("Bertrand", 350),
            ("Kemp", 350),
            ("Lutgen", 349),
            ("Gillen", 348),
            ("Baustert", 346),
            ("Stoltz", 346),
            ("Lamesch", 345),
            ("Carvalho", 344),
            ("Reinert", 341),
            ("Schummer", 337),
            ("Hilger", 337),
            ("Michel", 333),
            ("Reiter", 330),
            ("Hubert", 329),
            ("Neu", 328),
            ("Dias", 326),
            ("Frisch", 322),
            ("Nosbusch", 322),
            ("Silva", 320),
            ("Weyrich", 320),
            ("Wilmes", 318),
            ("Brandenburger", 314),
            ("Manderscheid", 314),
            ("Pedersen", 313),
            ("Rollinger", 313),
            ("Eischen", 312),
            ("Kraus", 312),
            ("Paulus", 312),
            ("Kauffmann", 311),
            ("Colling", 310),
            ("Correia", 305),
            ("Koenig", 305),
            ("Glodt", 303),
            ("Antony", 301),
            ("Cardoso", 300),
            ("Oberweis", 298),
            ("Quintus", 297),
            ("Jost", 297),
            ("Agovic", 296),
            ("Machado", 295),
            ("Beffort", 293),
            ("Wiltzius", 292),
            ("Francois", 292),
            ("Maas", 291),
            ("Vitali", 291),
            ("Fischbach", 290),
            ("Reckinger", 289),
            ("Bauer", 288),
            ("Fisch", 288),
            ("Beck", 286),
            ("Andersen", 285),
            ("Delvaux", 284),
            ("Gloden", 281),
            ("Hames", 280),
            ("Ramdedovic", 280),
            ("Friederich", 279),
            ("Richard", 279),
            ("Melchior", 279),
            ("Zeimet", 278),
            ("Demuth", 276),
            ("Muratovic", 273),
            ("Ruppert", 273),
            ("Hurt", 269),
            ("Kass", 268),
            ("Hoss", 267),
            ("Rausch", 267),
            ("Thielen", 266),
            ("Andre", 265),
            ("Wampach", 265),
            ("Linster", 264),
            ("Dupont", 263),
            ("Dahm", 263),
            ("Willems", 263),
            ("Schartz", 260),
            ("Clees", 260),
            ("Fonck", 259),
            ("Wilhelm", 258),
            ("Jensen", 258),
            ("Petit", 258),
            ("Schank", 257),
            ("Kerger", 257),
            ("Franzen", 257),
            ("Gaspar", 256),
            ("Gilson", 256),
            ("Biwer", 255),
            ("Wolf", 254),
            ("Tavares", 253),
            ("Reiser", 253),
            ("De jesus", 252),
            ("Heintz", 250),
            ("Robert", 248),
            ("Goetzinger", 246),
            ("Schon", 246),
            ("Claude", 244),
            ("Halsdorf", 244),
            ("Moreira", 243),
            ("Schuler", 241),
            ("Schlesser", 241),
            ("Colbach", 241),
            ("Haupert", 240),
            ("Cikotic", 239),
            ("Rossi", 239),
            ("Siebenaler", 238),
            ("Daleiden", 238),
            ("Gaasch", 237),
            ("Lemmer", 237),
            ("Kasel", 236),
            ("Breuer", 235),
            ("Skenderovic", 234),
            ("Godart", 234),
            ("Bettendorff", 234),
            ("Karier", 233),
            ("Graf", 233),
            ("Louis", 233),
            ("Feinen", 233),
            ("Risch", 232),
            ("Weisgerber", 232),
            ("Beissel", 231),
            ("Mores", 230),
            ("Juncker", 229),
            ("Buchler", 229),
            ("Santos", 229),
            ("Feltz", 229),
            ("Pletschette", 228),
            ("Entringer", 228),
            ("Brosius", 227),
            ("Bintner", 227),
            ("Heirens", 226),
            ("Urbany", 226),
            ("Marnach", 226),
            ("Neumann", 225),
            ("Sauber", 225),
            ("Pundel", 225),
            ("Feyder", 225),
            ("Thomas", 224),
            ("Meisch", 224),
            ("Greisch", 224),
            ("Bruck", 224),
            ("Turmes", 224),
            ("Hemmen", 224),
            ("Hemmer", 222),
            ("Krecke", 221),
            ("Bintz", 220),
            ("Baum", 220),
            ("Gregoire", 219),
            ("Kinsch", 219),
            ("Gatti", 218),
            ("Schilling", 218),
            ("Schwartz", 217),
            ("Kaiser", 217),
            ("Zenner", 217),
            ("Thilmany", 217),
            ("Mathias", 215),
            ("Mayer", 214),
            ("Fuchs", 214),
            ("Kocan", 213),
            ("Staudt", 213),
            ("Franck", 213),
            ("Berscheid", 213),
            ("Hahn", 213),
            ("Strasser", 213),
            ("Frank", 212),
            ("Feltgen", 212),
            ("Goerens", 210),
            ("Ley", 209),
            ("Zeimes", 208),
            ("Lima", 208),
            ("Beckius", 207),
            ("Heuertz", 207),
            ("Feiereisen", 206),
            ("Krack", 206),
            ("Guillaume", 206),
            ("Pires", 206),
            ("Seil", 206),
            ("Kintziger", 205),
        )
    )

    # Source for first names: https://github.com/MatthiasWinkelmann/firstname-database
    first_names_female = OrderedDict(
        (
            ("Ada", 0.00390625),
            ("Adeline", 0.015625),
            ("Adrienne", 0.015625),
            ("Agnès", 0.0625),
            ("Albertine", 0.0625),
            ("Alice", 0.25),
            ("Aline", 0.0625),
            ("Aloyse", 0.5),
            ("Aly", 0.125),
            ("Amandine", 0.00390625),
            ("Amélie", 0.03125),
            ("Andréa", 0.0625),
            ("Andrée", 0.125),
            ("Angèle", 0.0625),
            ("Angélique", 0.015625),
            ("Anita", 0.03125),
            ("Anna", 0.25),
            ("Anne", 1.0),
            ("Annette", 0.125),
            ("Annick", 0.125),
            ("Annie", 0.03125),
            ("Anouk", 0.0625),
            ("Antoinette", 0.125),
            ("Ariane", 0.015625),
            ("Arlette", 0.0625),
            ("Armande", 0.00390625),
            ("Armelle", 0.0078125),
            ("Astrid", 0.125),
            ("Astride", 0.015625),
            ("Audrey", 0.03125),
            ("Aurélie", 0.015625),
            ("Barbara", 0.0625),
            ("Béatrice", 0.0625),
            ("Béatrix", 0.00390625),
            ("Bénédicte", 0.015625),
            ("Bernadette", 0.03125),
            ("Berthe", 0.03125),
            ("Betty", 0.0625),
            ("Bianca", 0.03125),
            ("Birgit", 0.015625),
            ("Blanche", 0.0625),
            ("Blandine", 0.00390625),
            ("Brigitte", 0.125),
            ("Camille", 0.5),
            ("Carine", 0.125),
            ("Carol", 0.015625),
            ("Carole", 0.25),
            ("Caroline", 0.125),
            ("Catherine", 0.5),
            ("Cécile", 0.25),
            ("Cecilia", 0.0078125),
            ("Cecille", 0.00390625),
            ("Céline", 0.125),
            ("Chantal", 0.25),
            ("Chloe", 0.00390625),
            ("Christelle", 0.03125),
            ("Christiane", 0.5),
            ("Christine", 0.125),
            ("Cindy", 0.0625),
            ("Claire", 0.125),
            ("Clarisse", 0.00390625),
            ("Claudette", 0.0078125),
            ("Claudia", 0.0625),
            ("Claudie", 0.00390625),
            ("Claudine", 0.25),
            ("Clémentine", 0.00390625),
            ("Clothilde", 0.0078125),
            ("Clotilde", 0.00390625),
            ("Colette", 0.125),
            ("Constance", 0.0078125),
            ("Corinne", 0.0625),
            ("Cornelia", 0.015625),
            ("Cynthia", 0.03125),
            ("Damienne", 0.00390625),
            ("Daniela", 0.0625),
            ("Danièle", 0.125),
            ("Danielle", 0.25),
            ("Dany", 0.0625),
            ("Deborah", 0.03125),
            ("Delphine", 0.03125),
            ("Denise", 0.25),
            ("Désirée", 0.015625),
            ("Diane", 0.125),
            ("Doris", 0.0625),
            ("Dorothée", 0.0078125),
            ("Eléonore", 0.0078125),
            ("Eliane", 0.03125),
            ("Eliette", 0.0078125),
            ("Elisabeth", 0.25),
            ("Elise", 0.125),
            ("Elodie", 0.00390625),
            ("Elvira", 0.03125),
            ("Elvire", 0.03125),
            ("Emilie", 0.0625),
            ("Emma", 0.015625),
            ("Emmanuelle", 0.03125),
            ("Ernestine", 0.015625),
            ("Erny", 0.25),
            ("Estelle", 0.03125),
            ("Esther", 0.03125),
            ("Eugénie", 0.0625),
            ("Eunice", 0.0078125),
            ("Eva", 0.03125),
            ("Fabienne", 0.125),
            ("Fanny", 0.015625),
            ("Félicie", 0.0625),
            ("Fernande", 0.125),
            ("Ferny", 0.0078125),
            ("Flore", 0.00390625),
            ("Florence", 0.0625),
            ("Florentine", 0.0078125),
            ("France", 0.125),
            ("Francine", 0.125),
            ("Françoise", 0.25),
            ("Frédérique", 0.03125),
            ("Gabrielle", 0.0625),
            ("Gaby", 0.0625),
            ("Gaëlle", 0.0078125),
            ("Geneviève", 0.03125),
            ("Georgette", 0.125),
            ("Géraldine", 0.03125),
            ("Germaine", 0.125),
            ("Gertrude", 0.015625),
            ("Ghislaine", 0.015625),
            ("Gilberte", 0.03125),
            ("Ginette", 0.125),
            ("Gisèle", 0.0625),
            ("Hélène", 0.25),
            ("Heloise", 0.00390625),
            ("Henriette", 0.25),
            ("Hilda", 0.03125),
            ("Huguette", 0.015625),
            ("Ida", 0.03125),
            ("Inès", 0.015625),
            ("Ingrid", 0.03125),
            ("Irène", 0.25),
            ("Irma", 0.0625),
            ("Isabel", 0.125),
            ("Isabelle", 0.5),
            ("Jacqueline", 0.25),
            ("Janine", 0.015625),
            ("Jasmine", 0.015625),
            ("Jeanette", 0.0078125),
            ("Jeanine", 0.015625),
            ("Jeanne", 0.5),
            ("Jeannette", 0.03125),
            ("Jeannie", 0.00390625),
            ("Jeannine", 0.0625),
            ("Jeanny", 0.125),
            ("Jennifer", 0.03125),
            ("Jessica", 0.125),
            ("Jocelyne", 0.015625),
            ("Joëlle", 0.25),
            ("Josée", 0.5),
            ("Joséphine", 0.125),
            ("Josette", 0.25),
            ("Josiane", 0.125),
            ("Josy", 0.5),
            ("Judith", 0.03125),
            ("Julia", 0.03125),
            ("Julie", 0.125),
            ("Julienne", 0.0078125),
            ("Juliette", 0.0625),
            ("Justine", 0.015625),
            ("Karin", 0.125),
            ("Karine", 0.03125),
            ("Katia", 0.0078125),
            ("Kim", 0.0625),
            ("Laetitia", 0.015625),
            ("Laura", 0.0078125),
            ("Laure", 0.0625),
            ("Laurence", 0.125),
            ("Laurette", 0.00390625),
            ("Léa", 0.0625),
            ("Léone", 0.00390625),
            ("Léonie", 0.125),
            ("Léontine", 0.015625),
            ("Liliane", 0.25),
            ("Lily", 0.03125),
            ("Lina", 0.0625),
            ("Linda", 0.125),
            ("Louise", 0.25),
            ("Lucette", 0.0078125),
            ("Lucie", 0.125),
            ("Lucienne", 0.0625),
            ("Ludivine", 0.00390625),
            ("Lydia", 0.03125),
            ("Lydiane", 0.00390625),
            ("Lydianne", 0.00390625),
            ("Lydie", 0.0625),
            ("Lysiane", 0.00390625),
            ("Madeleine", 0.125),
            ("Magali", 0.015625),
            ("Magalie", 0.00390625),
            ("Maggy", 0.125),
            ("Maisy", 0.125),
            ("Malou", 0.0625),
            ("Manuela", 0.03125),
            ("Manuelle", 0.00390625),
            ("Marceline", 0.015625),
            ("Marcelle", 0.125),
            ("Margot", 0.25),
            ("Marguerite", 0.25),
            ("Maria", 2.0),
            ("Marianne", 0.25),
            ("Marie", 4.0),
            ("Marielle", 0.015625),
            ("Mariette", 0.25),
            ("Marine", 0.00390625),
            ("Marion", 0.0625),
            ("Marise", 0.00390625),
            ("Marlène", 0.03125),
            ("Marlyse", 0.00390625),
            ("Marthe", 0.125),
            ("Martine", 0.5),
            ("Marylène", 0.0078125),
            ("Maryline", 0.0078125),
            ("Maryse", 0.0625),
            ("Maryvonne", 0.00390625),
            ("Mathilde", 0.03125),
            ("Mauricette", 0.00390625),
            ("Mélanie", 0.0625),
            ("Michèle", 0.5),
            ("Micheline", 0.0625),
            ("Michelle", 0.03125),
            ("Mimy", 0.00390625),
            ("Mireille", 0.125),
            ("Monika", 0.03125),
            ("Monique", 0.5),
            ("Morgane", 0.00390625),
            ("Muriel", 0.0625),
            ("Murielle", 0.03125),
            ("Mylène", 0.015625),
            ("Myriam", 0.125),
            ("Nadège", 0.0078125),
            ("Nadia", 0.03125),
            ("Nadine", 0.25),
            ("Nancy", 0.0625),
            ("Natacha", 0.015625),
            ("Nathalie", 0.5),
            ("Nelly", 0.125),
            ("Nicole", 0.5),
            ("Nina", 0.03125),
            ("Noëlle", 0.015625),
            ("Noémie", 0.0078125),
            ("Nora", 0.015625),
            ("Octavie", 0.0078125),
            ("Odette", 0.0625),
            ("Odile", 0.0625),
            ("Olga", 0.03125),
            ("Pascale", 0.0625),
            ("Patricia", 0.25),
            ("Paule", 0.125),
            ("Paulette", 0.0625),
            ("Pauline", 0.0625),
            ("Peggy", 0.0625),
            ("Petra", 0.03125),
            ("Pierette", 0.00390625),
            ("Pierrette", 0.0625),
            ("Rachel", 0.03125),
            ("Rachèle", 0.00390625),
            ("Raphaëlle", 0.0078125),
            ("Raymonde", 0.0625),
            ("Regina", 0.015625),
            ("Régine", 0.0625),
            ("Reine", 0.00390625),
            ("Rejane", 0.0078125),
            ("Renée", 0.25),
            ("Rita", 0.125),
            ("Rolande", 0.0078125),
            ("Rollande", 0.00390625),
            ("Romaine", 0.0625),
            ("Rosa", 0.015625),
            ("Rosalie", 0.015625),
            ("Rose", 0.125),
            ("Rosy", 0.015625),
            ("Roxane", 0.00390625),
            ("Roxanne", 0.00390625),
            ("Ruth", 0.015625),
            ("Sabine", 0.03125),
            ("Sandra", 0.5),
            ("Sandrine", 0.0625),
            ("Sandy", 0.0625),
            ("Sarah", 0.0625),
            ("Scarlette", 0.00390625),
            ("Severine", 0.03125),
            ("Simone", 0.125),
            ("Simonne", 0.00390625),
            ("Solange", 0.03125),
            ("Sonia", 0.03125),
            ("Sophie", 0.125),
            ("Stéphanie", 0.125),
            ("Susanne", 0.03125),
            ("Suzanne", 0.125),
            ("Suzette", 0.125),
            ("Sylvaine", 0.00390625),
            ("Sylvia", 0.015625),
            ("Sylviane", 0.015625),
            ("Sylvie", 0.5),
            ("Thérèse", 0.25),
            ("Tina", 0.0625),
            ("Ursula", 0.03125),
            ("Valérie", 0.125),
            ("Vera", 0.03125),
            ("Véronique", 0.25),
            ("Vicky", 0.03125),
            ("Victorine", 0.015625),
            ("Vinciane", 0.015625),
            ("Violette", 0.00390625),
            ("Virginie", 0.0625),
            ("Viviane", 0.25),
            ("Vivienne", 0.00390625),
            ("Yolande", 0.0625),
            ("Yvette", 0.125),
            ("Yvonne", 0.25),
        )
    )

    first_names_male = OrderedDict(
        (
            ("Achille", 0.00390625),
            ("Adolphe", 0.015625),
            ("Adrien", 0.0625),
            ("Aimable", 0.00390625),
            ("Alain", 0.5),
            ("Albert", 0.5),
            ("Alex", 0.25),
            ("Alexandre", 0.0625),
            ("Alexis", 0.0078125),
            ("Alfred", 0.0625),
            ("Aloïs", 0.00390625),
            ("Alphonse", 0.25),
            ("André", 1.0),
            ("Andreas", 0.015625),
            ("Ange", 0.00390625),
            ("Anicet", 0.00390625),
            ("Anthony", 0.015625),
            ("Antoine", 0.5),
            ("Aristide", 0.00390625),
            ("Armand", 0.5),
            ("Arnaud", 0.015625),
            ("Arnold", 0.03125),
            ("Arthur", 0.125),
            ("Auguste", 0.03125),
            ("Aurelien", 0.00390625),
            ("Axel", 0.0078125),
            ("Baptiste", 0.015625),
            ("Bastien", 0.00390625),
            ("Benoît", 0.0625),
            ("Bernard", 0.5),
            ("Bernd", 0.015625),
            ("Bertrand", 0.03125),
            ("Bruno", 0.0625),
            ("Carlo", 0.125),
            ("Cédric", 0.03125),
            ("Célestin", 0.0078125),
            ("Charles", 0.5),
            ("Charly", 0.00390625),
            ("Christian", 0.25),
            ("Christophe", 0.125),
            ("Claude", 1.0),
            ("Clement", 0.015625),
            ("Constant", 0.03125),
            ("Corneille", 0.015625),
            ("Cornel", 0.00390625),
            ("Cyril", 0.0078125),
            ("Damien", 0.015625),
            ("Dan", 0.03125),
            ("Daniel", 0.25),
            ("David", 0.125),
            ("Denis", 0.0625),
            ("Désiré", 0.0078125),
            ("Didier", 0.125),
            ("Dieter", 0.015625),
            ("Dimitri", 0.00390625),
            ("Edgar", 0.015625),
            ("Edgard", 0.0078125),
            ("Edmond", 0.125),
            ("Edouard", 0.125),
            ("Elie", 0.00390625),
            ("Eloi", 0.0078125),
            ("Emile", 0.5),
            ("Emmanuel", 0.03125),
            ("Eric", 0.125),
            ("Erik", 0.015625),
            ("Ernest", 0.25),
            ("Erwin", 0.015625),
            ("Etienne", 0.0625),
            ("Eugène", 0.25),
            ("Fabien", 0.03125),
            ("Fabrice", 0.5),
            ("Felicien", 0.00390625),
            ("Félix", 0.125),
            ("Ferdinand", 0.03125),
            ("Fernand", 1.0),
            ("Firmin", 0.00390625),
            ("Florent", 0.03125),
            ("Francis", 0.125),
            ("Franck", 0.03125),
            ("François", 1.0),
            ("Frank", 0.25),
            ("Franky", 0.0078125),
            ("Franz", 0.015625),
            ("Freddy", 0.0078125),
            ("Frédéric", 0.125),
            ("Frederick", 0.00390625),
            ("Gabriel", 0.015625),
            ("Gaël", 0.00390625),
            ("Gaston", 0.25),
            ("Georges", 0.5),
            ("Gérald", 0.0078125),
            ("Gérard", 0.25),
            ("Geraud", 0.00390625),
            ("Gery", 0.00390625),
            ("Ghislain", 0.0078125),
            ("Gilbert", 0.25),
            ("Gilles", 0.125),
            ("Grégoire", 0.015625),
            ("Grégory", 0.015625),
            ("Guillaume", 0.125),
            ("Guy", 1.0),
            ("Gwenael", 0.00390625),
            ("Hans", 0.0625),
            ("Heinz", 0.03125),
            ("Helmut", 0.015625),
            ("Henri", 0.5),
            ("Henrique", 0.015625),
            ("Henry", 0.03125),
            ("Herbert", 0.015625),
            ("Hermann", 0.015625),
            ("Hervé", 0.03125),
            ("Hugo", 0.015625),
            ("Hugues", 0.0078125),
            ("Ignace", 0.0078125),
            ("Jacky", 0.0078125),
            ("Jacques", 0.5),
            ("James", 0.015625),
            ("Jean", 4.0),
            ("Jean-Claude", 0.25),
            ("Jean-Luc", 0.0625),
            ("Jeannot", 0.25),
            ("Jean-Paul", 0.25),
            ("Jean-Pierre", 0.25),
            ("Jeff", 0.0625),
            ("Jeremie", 0.00390625),
            ("Jérôme", 0.0625),
            ("Jim", 0.03125),
            ("Joachim", 0.015625),
            ("Joé", 0.0625),
            ("Joël", 0.125),
            ("John", 0.25),
            ("Johnny", 0.015625),
            ("Johny", 0.125),
            ("Jonathan", 0.015625),
            ("Jorge", 0.0625),
            ("Joseph", 0.5),
            ("Jules", 0.125),
            ("Julien", 0.0625),
            ("Jürgen", 0.015625),
            ("Justin", 0.015625),
            ("Karl", 0.015625),
            ("Kevin", 0.0078125),
            ("Klaus", 0.03125),
            ("Kurt", 0.015625),
            ("Lambert", 0.015625),
            ("Laurent", 0.25),
            ("Léandre", 0.0078125),
            ("Léo", 0.03125),
            ("Léon", 0.5),
            ("Léonard", 0.0078125),
            ("Léonce", 0.00390625),
            ("Léopold", 0.015625),
            ("Lionel", 0.015625),
            ("Loïc", 0.0078125),
            ("Louis", 0.25),
            ("Luc", 0.25),
            ("Lucien", 0.5),
            ("Ludovic", 0.0078125),
            ("Manfred", 0.015625),
            ("Manuel", 0.125),
            ("Marc", 1.0),
            ("Marcel", 1.0),
            ("Marco", 0.25),
            ("Marguy", 0.0078125),
            ("Marius", 0.0078125),
            ("Martial", 0.0078125),
            ("Martin", 0.0625),
            ("Mathias", 0.125),
            ("Mathieu", 0.0078125),
            ("Matthieu", 0.00390625),
            ("Maurice", 0.0625),
            ("Max", 0.015625),
            ("Maxime", 0.015625),
            ("Maximilien", 0.00390625),
            ("Michael", 0.0625),
            ("Michaël", 0.0078125),
            ("Michel", 1.0),
            ("Mickael", 0.00390625),
            ("Mike", 0.125),
            ("Narcisse", 0.0078125),
            ("Nicolas", 0.5),
            ("Noël", 0.015625),
            ("Norbert", 0.25),
            ("Olivier", 0.125),
            ("Oswald", 0.00390625),
            ("Pascal", 0.125),
            ("Patrice", 0.0625),
            ("Patrick", 0.5),
            ("Paul", 0.5),
            ("Peter", 0.0625),
            ("Philippe", 0.25),
            ("Pierre", 2.0),
            ("Ralph", 0.0625),
            ("Raoul", 0.015625),
            ("Raphaël", 0.03125),
            ("Raymond", 0.5),
            ("Réginald", 0.00390625),
            ("Régis", 0.0078125),
            ("Rémi", 0.0078125),
            ("Rémy", 0.0625),
            ("Renaud", 0.0078125),
            ("René", 1.0),
            ("Richard", 0.125),
            ("Robert", 0.5),
            ("Rodolphe", 0.015625),
            ("Roger", 1.0),
            ("Roland", 0.25),
            ("Romain", 0.5),
            ("Ronald", 0.03125),
            ("Rudy", 0.0625),
            ("Samuel", 0.0078125),
            ("Sébastien", 0.03125),
            ("Serge", 0.25),
            ("Severin", 0.00390625),
            ("Séverin", 0.00390625),
            ("Simon", 0.0078125),
            ("Stefan", 0.015625),
            ("Stephan", 0.03125),
            ("Stéphane", 0.0625),
            ("Steven", 0.0078125),
            ("Sylvain", 0.0625),
            ("Sylvère", 0.0078125),
            ("Tanguy", 0.00390625),
            ("Teddy", 0.00390625),
            ("Théo", 0.25),
            ("Théodore", 0.03125),
            ("Théophile", 0.015625),
            ("Thibaud", 0.00390625),
            ("Thibaut", 0.00390625),
            ("Thierry", 0.125),
            ("Thomas", 0.0625),
            ("Tommy", 0.0078125),
            ("Valéry", 0.00390625),
            ("Victor", 0.25),
            ("Vincent", 0.0625),
            ("Vivien", 0.00390625),
            ("Werner", 0.03125),
            ("William", 0.015625),
            ("Willy", 0.0625),
            ("Wolfgang", 0.03125),
            ("Xavier", 0.03125),
            ("Yann", 0.015625),
            ("Yannick", 0.015625),
            ("Yvan", 0.015625),
            ("Yves", 0.25),
            ("Yvon", 0.03125),
        )
    )

    first_names_nonbinary = OrderedDict(
        [("Claudy", 0.00390625), ("Cyrille", 0.0078125), ("Dominique", 0.125)]
        + list(first_names_female.items())
        + list(first_names_male.items())
    )
