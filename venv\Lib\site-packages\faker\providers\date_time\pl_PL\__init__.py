from .. import Provider as DateTimeProvider


class Provider(DateTimeProvider):
    DAY_NAMES = {
        "0": "ponied<PERSON><PERSON><PERSON>",
        "1": "wtorek",
        "2": "<PERSON>rod<PERSON>",
        "3": "c<PERSON><PERSON><PERSON>",
        "4": "pi<PERSON><PERSON><PERSON>",
        "5": "sobota",
        "6": "nied<PERSON><PERSON>",
    }

    MONTH_NAMES = {
        "01": "styczeń",
        "02": "luty",
        "03": "marzec",
        "04": "kwiecie<PERSON>",
        "05": "maj",
        "06": "czerwiec",
        "07": "lipiec",
        "08": "sierpie<PERSON>",
        "09": "wrzesie<PERSON>",
        "10": "pa<PERSON><PERSON><PERSON><PERSON>",
        "11": "listopad",
        "12": "grudzie<PERSON>",
    }

    def day_of_week(self):
        day = self.date("%w")
        return self.DAY_NAMES[day]

    def month_name(self):
        month = self.month()
        return self.MONTH_NAMES[month]
